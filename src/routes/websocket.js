import express from 'express';
import { verifyToken } from '../middleware/auth.js';
import { query } from '../config/database.js';

const router = express.Router();

// Get WebSocket connection info
router.get('/info', verifyToken, async (req, res) => {
    try {
        const wsInfo = {
            endpoint: `ws://${req.get('host')}/ws`,
            protocols: ['websocket'],
            authentication: {
                methods: ['jwt_token', 'api_key'],
                jwt_parameter: 'token',
                api_key_parameter: 'apiKey'
            },
            connection_limits: {
                max_connections: req.user.max_websocket_connections,
                current_tier: req.user.tier_name
            },
            available_streams: req.user.allowed_streams,
            message_types: {
                subscribe: {
                    type: 'subscribe',
                    payload: { stream: 'stream_name' }
                },
                unsubscribe: {
                    type: 'unsubscribe',
                    payload: { stream: 'stream_name' }
                },
                ping: {
                    type: 'ping'
                }
            },
            example_connection: {
                url: `ws://${req.get('host')}/ws?token=YOUR_JWT_TOKEN`,
                alternative_url: `ws://${req.get('host')}/ws?apiKey=YOUR_API_KEY`
            }
        };
        
        res.json({
            success: true,
            websocket: wsInfo
        });
        
    } catch (error) {
        console.error('WebSocket info error:', error);
        res.status(500).json({
            error: 'Failed to get WebSocket info'
        });
    }
});

// Get available streams
router.get('/streams', verifyToken, async (req, res) => {
    try {
        // Get all streams that user has access to
        const userStreams = req.user.allowed_streams;
        
        let streamQuery;
        let queryParams = [];
        
        if (userStreams.includes('*')) {
            // User has access to all streams
            streamQuery = `
                SELECT sd.*, at.name as required_tier_name
                FROM stream_definitions sd
                LEFT JOIN access_tiers at ON sd.required_tier_id = at.id
                WHERE sd.is_active = true
                ORDER BY sd.stream_name
            `;
        } else {
            // User has access to specific streams
            streamQuery = `
                SELECT sd.*, at.name as required_tier_name
                FROM stream_definitions sd
                LEFT JOIN access_tiers at ON sd.required_tier_id = at.id
                WHERE sd.is_active = true AND sd.stream_name = ANY($1)
                ORDER BY sd.stream_name
            `;
            queryParams = [userStreams];
        }
        
        const result = await query(streamQuery, queryParams);
        
        const streams = result.rows.map(stream => ({
            name: stream.stream_name,
            description: stream.description,
            required_tier: stream.required_tier_name,
            credits_per_message: stream.credits_per_message,
            max_subscribers: stream.max_subscribers,
            metadata: stream.metadata,
            has_access: true
        }));
        
        res.json({
            success: true,
            streams,
            user_tier: req.user.tier_name,
            total_available: streams.length
        });
        
    } catch (error) {
        console.error('Streams list error:', error);
        res.status(500).json({
            error: 'Failed to get streams list'
        });
    }
});

// Get user's active WebSocket sessions
router.get('/sessions', verifyToken, async (req, res) => {
    try {
        const result = await query(
            `SELECT session_id, connection_id, subscribed_streams, 
                    connected_at, last_activity, ip_address
             FROM websocket_sessions 
             WHERE user_id = $1 AND disconnected_at IS NULL
             ORDER BY connected_at DESC`,
            [req.user.id]
        );
        
        const sessions = result.rows.map(session => ({
            session_id: session.session_id,
            connection_id: session.connection_id,
            subscribed_streams: session.subscribed_streams || [],
            connected_at: session.connected_at,
            last_activity: session.last_activity,
            ip_address: session.ip_address,
            duration_ms: Date.now() - new Date(session.connected_at).getTime()
        }));
        
        res.json({
            success: true,
            active_sessions: sessions,
            total_sessions: sessions.length,
            max_allowed: req.user.max_websocket_connections
        });
        
    } catch (error) {
        console.error('Sessions list error:', error);
        res.status(500).json({
            error: 'Failed to get WebSocket sessions'
        });
    }
});

// Disconnect a specific WebSocket session
router.delete('/sessions/:sessionId', verifyToken, async (req, res) => {
    try {
        const { sessionId } = req.params;
        
        // Verify session belongs to user
        const sessionResult = await query(
            'SELECT connection_id FROM websocket_sessions WHERE session_id = $1 AND user_id = $2 AND disconnected_at IS NULL',
            [sessionId, req.user.id]
        );
        
        if (sessionResult.rows.length === 0) {
            return res.status(404).json({
                error: 'Session not found or already disconnected'
            });
        }
        
        // Mark session as disconnected in database
        await query(
            'UPDATE websocket_sessions SET disconnected_at = CURRENT_TIMESTAMP WHERE session_id = $1',
            [sessionId]
        );
        
        // Note: In a real implementation, you would also need to close the actual WebSocket connection
        // This would require access to the WebSocket server instance
        
        res.json({
            success: true,
            message: 'Session disconnected successfully',
            session_id: sessionId
        });
        
    } catch (error) {
        console.error('Session disconnect error:', error);
        res.status(500).json({
            error: 'Failed to disconnect session'
        });
    }
});

// Get WebSocket usage statistics
router.get('/stats', verifyToken, async (req, res) => {
    try {
        const { days = 7 } = req.query;
        
        // Get connection statistics
        const statsResult = await query(
            `SELECT 
                DATE_TRUNC('day', connected_at) as date,
                COUNT(*) as total_connections,
                COUNT(DISTINCT session_id) as unique_sessions,
                AVG(EXTRACT(EPOCH FROM (COALESCE(disconnected_at, CURRENT_TIMESTAMP) - connected_at))) as avg_duration_seconds
             FROM websocket_sessions 
             WHERE user_id = $1 AND connected_at >= CURRENT_DATE - INTERVAL '${parseInt(days)} days'
             GROUP BY DATE_TRUNC('day', connected_at)
             ORDER BY date DESC`,
            [req.user.id]
        );
        
        // Get stream subscription statistics
        const streamStatsResult = await query(
            `SELECT 
                unnest(subscribed_streams) as stream_name,
                COUNT(*) as subscription_count
             FROM websocket_sessions 
             WHERE user_id = $1 AND connected_at >= CURRENT_DATE - INTERVAL '${parseInt(days)} days'
               AND subscribed_streams IS NOT NULL
             GROUP BY unnest(subscribed_streams)
             ORDER BY subscription_count DESC`,
            [req.user.id]
        );
        
        const stats = {
            period_days: parseInt(days),
            daily_stats: statsResult.rows.map(row => ({
                date: row.date,
                total_connections: parseInt(row.total_connections),
                unique_sessions: parseInt(row.unique_sessions),
                avg_duration_seconds: parseFloat(row.avg_duration_seconds) || 0
            })),
            stream_subscriptions: streamStatsResult.rows.map(row => ({
                stream_name: row.stream_name,
                subscription_count: parseInt(row.subscription_count)
            })),
            summary: {
                total_connections: statsResult.rows.reduce((sum, row) => sum + parseInt(row.total_connections), 0),
                total_unique_sessions: statsResult.rows.reduce((sum, row) => sum + parseInt(row.unique_sessions), 0),
                most_popular_stream: streamStatsResult.rows[0]?.stream_name || null
            }
        };
        
        res.json({
            success: true,
            websocket_stats: stats
        });
        
    } catch (error) {
        console.error('WebSocket stats error:', error);
        res.status(500).json({
            error: 'Failed to get WebSocket statistics'
        });
    }
});

// Test WebSocket connection endpoint
router.post('/test', verifyToken, async (req, res) => {
    try {
        const testInfo = {
            message: 'WebSocket test endpoint',
            user: {
                id: req.user.id,
                email: req.user.email,
                tier: req.user.tier_name
            },
            connection_info: {
                max_connections: req.user.max_websocket_connections,
                allowed_streams: req.user.allowed_streams
            },
            test_urls: {
                with_jwt: `ws://${req.get('host')}/ws?token=YOUR_JWT_TOKEN`,
                with_api_key: `ws://${req.get('host')}/ws?apiKey=${req.user.api_key}`
            },
            sample_messages: {
                subscribe: JSON.stringify({
                    type: 'subscribe',
                    payload: { stream: 'demo-stream' }
                }),
                unsubscribe: JSON.stringify({
                    type: 'unsubscribe',
                    payload: { stream: 'demo-stream' }
                }),
                ping: JSON.stringify({
                    type: 'ping'
                })
            }
        };
        
        res.json({
            success: true,
            test_info: testInfo
        });
        
    } catch (error) {
        console.error('WebSocket test error:', error);
        res.status(500).json({
            error: 'WebSocket test failed'
        });
    }
});

export default router;
