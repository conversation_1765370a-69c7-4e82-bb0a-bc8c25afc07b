import { pubsub } from "../config/redis.js";
import { KolFeed } from "../workers/kolFeed.js";
import { query } from "../config/database.js";
import { cache, redis } from "../config/redis.js";

export class StreamManager {
  constructor() {
    this.streams = new Map();
    this.intervals = new Map();
    this.isRunning = false;
    this.kolFeed = null;

    this.initializeStreams();
  }

  // Initialize demo streams
  initializeStreams() {
    (async () => {
      // reset all websocket sessions
      await query(
        "UPDATE websocket_sessions SET disconnected_at = NOW() WHERE disconnected_at IS NULL"
      );
      // clear all ws_connections:* keys from Redis
      const keys = await redis.keys('ws_connections:*');
      if (keys.length > 0) {
        await redis.del(...keys);
        console.log(`✅ Cleared ${keys.length} ws_connections cache keys`);
      } else {
        console.log('ℹ️ No ws_connections cache keys found');
      }
    })();

    // Demo stream - simple counter and random data
    this.registerStream("demo-stream", {
      interval: parseInt(process.env.DEMO_STREAM_INTERVAL) || 5000,
      generator: this.generateDemoData.bind(this),
    });

    // Data stream - mock market data
    this.registerStream("data-stream", {
      interval: 2000,
      generator: this.generateMarketData.bind(this),
    });

    // Analytics stream - system metrics
    this.registerStream("analytics-stream", {
      interval: 10000,
      generator: this.generateAnalyticsData.bind(this),
    });

    // Enterprise stream - high frequency data
    this.registerStream("enterprise-stream", {
      interval: 1000,
      generator: this.generateEnterpriseData.bind(this),
    });

    // KOL Feed stream
    this.registerStream("kol-feed", {
      isEventDriven: true,
    });

    console.log("✅ Stream Manager initialized with demo streams");
  }

  // Register a new stream
  registerStream(streamName, config) {
    this.streams.set(streamName, {
      name: streamName,
      interval: config.interval || 0,
      generator: config.generator || null,
      isEventDriven: config.isEventDriven || false,
      isActive: false,
      lastUpdate: null,
      messageCount: 0,
    });
  }

  // Start all streams
  async start() {
    if (this.isRunning) {
      console.log("Stream Manager is already running");
      return;
    }

    this.isRunning = true;

    // Initialize and start KolFeed
    this.kolFeed = new KolFeed();
    await this.kolFeed.init();

    // Set up KolFeed message handler
    this.kolFeed.ws.on("message", (message) => {
      const json = JSON.parse(message);
      if (json?.tx && json?.source === "kol" && json?.isKol) {
        this.handleEventDrivenData("kol-feed", {
          data: {
            timestamp: json?.timestamp,
            kol_label: json?.wallet_label,
            wallet: json?.isPublic ? json?.walletAddress : "private",
            kol_avatar: json?.wallet_avatar,
            tokenIn: {
              symbol: json?.tx?.tokenIn?.symbol,
              name: json?.tx?.tokenIn?.name,
              logo: json?.tx?.tokenIn?.logo,
              tokenAmountString: json?.tx?.tokenIn?.tokenAmountString,
              amount: json?.tx?.tokenIn?.amount,
              tokenInAmountUsd: json?.tx?.tokenIn?.tokenInAmountUsd,
              price: json?.tx?.tokenIn?.price,
              mint: json?.tx?.tokenIn?.tokenAdress,
            },
            tokenOut: {
              symbol: json?.tx?.tokenOut?.symbol,
              name: json?.tx?.tokenOut?.name,
              logo: json?.tx?.tokenOut?.logo,
              tokenAmountString: json?.tx?.tokenOut?.tokenAmountString,
              amount: json?.tx?.tokenOut?.amount,
              tokenOutAmountUsd: json?.tx?.tokenOut?.tokenOutAmountUsd,
              price: json?.tx?.tokenOut?.price,
              mint: json?.tx?.tokenOut?.tokenAdress,
            },
            signature: json?.isPublic ? json?.tx : "private",
          },
          timestamp: json?.timestamp,
        });
      }
    });

    // Start all streams
    for (const [streamName, stream] of this.streams) {
      if (stream.isEventDriven) {
        // Mark event-driven streams as active
        stream.isActive = true;
        console.log(`✅ Event-driven stream started: ${streamName}`);
      } else if (stream.generator) {
        // Start interval-based streams
        this.startIntervalStream(streamName);
      } else {
        console.warn(
          `⚠️ Stream ${streamName} has no generator and is not event-driven`
        );
      }
    }

    console.log("✅ All streams started");
  }

  // Stop all streams
  async stop() {
    if (!this.isRunning) {
      console.log("Stream Manager is not running");
      return;
    }

    this.isRunning = false;

    // Stop KolFeed
    if (this.kolFeed) {
      this.kolFeed.stop();
      this.kolFeed = null;
    }

    // Stop all interval-based streams
    for (const [streamName, stream] of this.streams) {
      if (!stream.isActive) {
        console.log(`Stream ${streamName} is not active`);
        continue;
      }

      this.stopIntervalStream(streamName);
    }

    console.log("🛑 All streams stopped");
  }

  // Start an interval-based stream
  startIntervalStream(streamName) {
    const stream = this.streams.get(streamName);
    if (!stream) {
      console.error(`Stream not found: ${streamName}`);
      return;
    }

    if (stream.isActive) {
      console.log(`Stream ${streamName} is already active`);
      return;
    }

    if (stream.isEventDriven) {
      console.log(
        `Cannot start event-driven stream ${streamName} with interval`
      );
      return;
    }

    const interval = setInterval(async () => {
      try {
        const data = await stream.generator();
        await this.publishStreamData(streamName, data);

        stream.lastUpdate = Date.now();
        stream.messageCount++;
      } catch (error) {
        console.error(`Error generating data for stream ${streamName}:`, error);
      }
    }, stream.interval);

    this.intervals.set(streamName, interval);
    stream.isActive = true;

    console.log(
      `✅ Interval stream started: ${streamName} (interval: ${stream.interval}ms)`
    );
  }

  // Stop an interval-based stream
  stopIntervalStream(streamName) {
    const stream = this.streams.get(streamName);
    if (!stream) {
      console.error(`Stream not found: ${streamName}`);
      return;
    }

    if (!stream.isActive) {
      console.log(`Stream ${streamName} is not active`);
      return;
    }

    const interval = this.intervals.get(streamName);
    if (interval) {
      clearInterval(interval);
      this.intervals.delete(streamName);
    }

    stream.isActive = false;
    console.log(`🛑 Interval stream stopped: ${streamName}`);
  }

  // Handle event-driven data
  async handleEventDrivenData(streamName, data) {
    const stream = this.streams.get(streamName);
    if (!stream || !stream.isActive) {
      return;
    }

    try {
      // Publish the data directly without additional wrapping
      await pubsub.publish("stream_data", {
        stream: streamName,
        data: data,
        timestamp: Date.now(),
      });
      stream.lastUpdate = Date.now();
      stream.messageCount++;
    } catch (error) {
      console.error(
        `Error handling event-driven data for stream ${streamName}:`,
        error
      );
    }
  }

  // Publish data to a stream via Redis
  async publishStreamData(streamName, data) {
    try {
      await pubsub.publish("stream_data", {
        stream: streamName,
        data,
        timestamp: Date.now(),
      });
    } catch (error) {
      console.error(`Error publishing to stream ${streamName}:`, error);
    }
  }

  // Generate demo data
  generateDemoData() {
    return {
      counter: Math.floor(Date.now() / 1000),
      random: Math.random(),
      timestamp: new Date().toISOString(),
      message: "Hello from demo stream!",
      data: {
        temperature: Math.round((Math.random() * 40 + 10) * 100) / 100,
        humidity: Math.round(Math.random() * 100 * 100) / 100,
        pressure: Math.round((Math.random() * 100 + 950) * 100) / 100,
      },
    };
  }

  // Generate mock market data
  generateMarketData() {
    const symbols = [
      "AAPL",
      "GOOGL",
      "MSFT",
      "AMZN",
      "TSLA",
      "META",
      "NVDA",
      "NFLX",
    ];
    const symbol = symbols[Math.floor(Math.random() * symbols.length)];
    const basePrice = 100 + Math.random() * 200;
    const change = (Math.random() - 0.5) * 10;

    return {
      symbol,
      price: Math.round((basePrice + change) * 100) / 100,
      change: Math.round(change * 100) / 100,
      changePercent: Math.round((change / basePrice) * 10000) / 100,
      volume: Math.floor(Math.random() * 1000000),
      timestamp: new Date().toISOString(),
      bid: Math.round((basePrice + change - 0.01) * 100) / 100,
      ask: Math.round((basePrice + change + 0.01) * 100) / 100,
    };
  }

  // Generate analytics data
  generateAnalyticsData() {
    return {
      metrics: {
        cpu_usage: Math.round(Math.random() * 100 * 100) / 100,
        memory_usage: Math.round(Math.random() * 100 * 100) / 100,
        disk_usage: Math.round(Math.random() * 100 * 100) / 100,
        network_in: Math.floor(Math.random() * 1000),
        network_out: Math.floor(Math.random() * 1000),
      },
      api_stats: {
        requests_per_minute: Math.floor(Math.random() * 1000),
        avg_response_time: Math.round(Math.random() * 500 * 100) / 100,
        error_rate: Math.round(Math.random() * 5 * 100) / 100,
        active_connections: Math.floor(Math.random() * 100),
      },
      timestamp: new Date().toISOString(),
    };
  }

  // Generate enterprise data
  generateEnterpriseData() {
    return {
      event_id: Math.random().toString(36).substring(2, 15),
      event_type: ["order", "trade", "quote", "news"][
        Math.floor(Math.random() * 4)
      ],
      priority: ["low", "medium", "high", "critical"][
        Math.floor(Math.random() * 4)
      ],
      data: {
        value: Math.random() * 1000000,
        quantity: Math.floor(Math.random() * 10000),
        source: ["exchange_a", "exchange_b", "exchange_c"][
          Math.floor(Math.random() * 3)
        ],
      },
      metadata: {
        region: ["us-east", "us-west", "eu-central", "asia-pacific"][
          Math.floor(Math.random() * 4)
        ],
        latency: Math.round(Math.random() * 50 * 100) / 100,
      },
      timestamp: new Date().toISOString(),
    };
  }

  // Get stream statistics
  getStreamStats() {
    const stats = {};

    for (const [streamName, stream] of this.streams) {
      stats[streamName] = {
        name: streamName,
        isActive: stream.isActive,
        interval: stream.interval,
        lastUpdate: stream.lastUpdate,
        messageCount: stream.messageCount,
        uptime:
          stream.isActive && stream.lastUpdate
            ? Date.now() - stream.lastUpdate
            : 0,
      };
    }

    return stats;
  }

  // Get list of available streams
  getAvailableStreams() {
    return Array.from(this.streams.keys());
  }

  // Check if stream exists
  streamExists(streamName) {
    return this.streams.has(streamName);
  }

  // Manually trigger a stream update (for testing)
  async triggerStreamUpdate(streamName) {
    const stream = this.streams.get(streamName);
    if (!stream) {
      throw new Error(`Stream not found: ${streamName}`);
    }

    try {
      const data = await stream.generator();
      await this.publishStreamData(streamName, data);

      stream.lastUpdate = Date.now();
      stream.messageCount++;

      return data;
    } catch (error) {
      console.error(`Error triggering stream update for ${streamName}:`, error);
      throw error;
    }
  }
}
