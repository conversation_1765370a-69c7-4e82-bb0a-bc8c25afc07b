import Redis from 'ioredis';
import dotenv from 'dotenv';

dotenv.config();

// Redis configuration
const redisConfig = {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD || undefined,
    db: parseInt(process.env.REDIS_DB) || 0,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
    lazyConnect: true,
};

// Create Redis instances
export const redis = new Redis(redisConfig);
export const redisPub = new Redis(redisConfig);
export const redisSub = new Redis(redisConfig);

// Redis connection event handlers
redis.on('connect', () => {
    console.log('✅ Redis connected successfully');
});

redis.on('error', (err) => {
    console.error('❌ Redis connection error:', err.message);
});

redis.on('ready', () => {
    console.log('✅ Redis ready for operations');
});

// Publisher connection handlers
redisPub.on('connect', () => {
    console.log('✅ Redis Publisher connected');
});

redisPub.on('error', (err) => {
    console.error('❌ Redis Publisher error:', err.message);
});

// Subscriber connection handlers
redisSub.on('connect', () => {
    console.log('✅ Redis Subscriber connected');
});

redisSub.on('error', (err) => {
    console.error('❌ Redis Subscriber error:', err.message);
});

// Test Redis connection
export const testRedisConnection = async () => {
    try {
        await redis.ping();
        console.log('✅ Redis ping successful');
        return true;
    } catch (err) {
        console.error('❌ Redis ping failed:', err.message);
        return false;
    }
};

// Cache helper functions
export const cache = {
    // Get value from cache
    get: async (key) => {
        try {
            const value = await redis.get(key);
            return value ? JSON.parse(value) : null;
        } catch (error) {
            console.error('Cache get error:', error);
            return null;
        }
    },

    // Set value in cache with TTL
    set: async (key, value, ttl = parseInt(process.env.CACHE_TTL) || 300) => {
        try {
            await redis.setex(key, ttl, JSON.stringify(value));
            return true;
        } catch (error) {
            console.error('Cache set error:', error);
            return false;
        }
    },

    // Delete value from cache
    del: async (key) => {
        try {
            await redis.del(key);
            return true;
        } catch (error) {
            console.error('Cache delete error:', error);
            return false;
        }
    },

    // Check if key exists
    exists: async (key) => {
        try {
            return await redis.exists(key);
        } catch (error) {
            console.error('Cache exists error:', error);
            return false;
        }
    },

    // Increment counter
    incr: async (key, ttl = 3600) => {
        try {
            const multi = redis.multi();
            multi.incr(key);
            multi.expire(key, ttl);
            const results = await multi.exec();
            return results[0][1]; // Return the incremented value
        } catch (error) {
            console.error('Cache increment error:', error);
            return null;
        }
    }
};

// Pub/Sub helper functions
export const pubsub = {
    // Publish message to channel
    publish: async (channel, message) => {
        try {
            const messageStr = typeof message === 'string' ? message : JSON.stringify(message);
            await redisPub.publish(channel, messageStr);
            return true;
        } catch (error) {
            console.error('Publish error:', error);
            return false;
        }
    },

    // Subscribe to channel
    subscribe: async (channel, callback) => {
        try {
            await redisSub.subscribe(channel);
            redisSub.on('message', (receivedChannel, message) => {
                if (receivedChannel === channel) {
                    try {
                        const parsedMessage = JSON.parse(message);
                        callback(parsedMessage);
                    } catch (error) {
                        callback(message); // If not JSON, return as string
                    }
                }
            });
            return true;
        } catch (error) {
            console.error('Subscribe error:', error);
            return false;
        }
    },

    // Unsubscribe from channel
    unsubscribe: async (channel) => {
        try {
            await redisSub.unsubscribe(channel);
            return true;
        } catch (error) {
            console.error('Unsubscribe error:', error);
            return false;
        }
    }
};

// Close Redis connections
export const closeRedis = async () => {
    await redis.quit();
    await redisPub.quit();
    await redisSub.quit();
    console.log('Redis connections closed');
};

export default redis;
