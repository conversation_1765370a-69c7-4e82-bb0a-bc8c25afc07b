import { pubsub } from "../config/redis.js";
import { WebSocket } from "ws";

export class KolFeed {
  constructor() {
    this.isRunning = false;
    this.ws = null;
  }

  async init() {
    this.isRunning = true;
    this.connect();
  }

  stop() {
    this.isRunning = false;
    if (this.ws) {
      this.ws.close();
    }
  }

  connect() {
    this.ws = new WebSocket(
      // `${process.env.STALKCHAIN_CENTRAL_WSS_URL}/feed?apiKey=${process.env.STALKCHAIN_CENTRAL_KEY}&sources=kol,user,stalks`
      `${process.env.STALKCHAIN_CENTRAL_WSS_URL}/feed?apiKey=${process.env.STALKCHAIN_CENTRAL_KEY}&sources=kol`
    );
    this.ws.on("open", () => {
      console.log("Connected to the KOL Feed WebSocket");
    });

    this.ws.on("message", (message) => {
      const json = JSON.parse(message);
      console.log("KOL FEED MESSAGE", JSON.stringify(json, null, 2));

      // Check if this is a valid KOL transaction
      if (json?.tx && json?.source === "kol" && json?.isKol) {
        // Transform the data to match our API format
        const transformedData = {
          timestamp: json?.timestamp,
          kol_label: json?.wallet_label,
          wallet: json?.isPublic ? json?.walletAddress : "private",
          kol_avatar: json?.wallet_avatar,
          tokenIn: {
            symbol: json?.tx?.tokenIn?.symbol,
            name: json?.tx?.tokenIn?.name,
            logo: json?.tx?.tokenIn?.logo,
            tokenAmountString: json?.tx?.tokenIn?.tokenAmountString,
            amount: json?.tx?.tokenIn?.amount,
            tokenInAmountUsd: json?.tx?.tokenIn?.tokenInAmountUsd,
            price: json?.tx?.tokenIn?.price,
            mint: json?.tx?.tokenIn?.tokenAddress || json?.tx?.tokenIn?.tokenAdress,
          },
          tokenOut: {
            symbol: json?.tx?.tokenOut?.symbol,
            name: json?.tx?.tokenOut?.name,
            logo: json?.tx?.tokenOut?.logo,
            tokenAmountString: json?.tx?.tokenOut?.tokenAmountString,
            amount: json?.tx?.tokenOut?.amount,
            tokenOutAmountUsd: json?.tx?.tokenOut?.tokenOutAmountUsd,
            price: json?.tx?.tokenOut?.price,
            mint: json?.tx?.tokenOut?.tokenAddress || json?.tx?.tokenOut?.tokenAdress,
          },
          signature: json?.isPublic ? json?.tx?.tx : "private",
          transactionType: json?.tx?.transactionType,
          chain: json?.tx?.chain,
          totalUsd: json?.tx?.totalUsd,
          totalUsdNumber: json?.tx?.totalUsdNumber,
        };

        // Publish to Redis using the stream_data channel that WebSocket server listens to
        pubsub.publish("stream_data", {
          stream: "kol-feed",
          data: transformedData,
          timestamp: Date.now(),
        });

        console.log(`✅ Published KOL feed message for ${json?.tx?.wallet_label || 'unknown'}`);
      }
    });

    this.ws.on("error", (error) => {
      console.error("WebSocket error:", error);
      this.close();
    });

    this.ws.on("close", () => {
      console.log("WebSocket connection closed");
      if (this.isRunning) {
        this.connect();
      }
    });
  }
}

/* 
{
  type: 'txSwap',
  source: 'kol',
  tx: {
    tokenIn: {
      symbol: 'SOL',
      name: 'Wrapped SOL',
      logo: 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png',
      tokenAmountString: '0.92',
      amount: 0.9197152919999994,
      tokenInAmountUsd: 146.465609,
      price: 159.251032,
      tokenAdress: 'So11111111111111111111111111111111111111112',
      tokenAddress: 'So11111111111111111111111111111111111111112'
    },
    tokenOut: {
      symbol: 'CAREBUBU',
      name: 'NEW CAREBEAR LABUBU DOLLS',
      logo: '',
      tokenAmountString: '16.71M',
      amount: 16708878.061636,
      tokenOutAmountUsd: 145.000953,
      price: 0.000009,
      tokenAdress: 'HRum1AZkwkG35nFBZavpHuadZHXVLss1bN8jp64Jpump',
      tokenAddress: 'HRum1AZkwkG35nFBZavpHuadZHXVLss1bN8jp64Jpump'
    },
    timestamp: 1748913012,
    chain: 'solana',
    tx: '5uTz1FZkUiZpe418qEUJ5a9w7bsnbhDNw7N4XouW9ufsTxBBQCE1FYqeipzjpcXqwWrH7Nji32hccBwso4NbErTr',
    walletAddress: '********************************************',
    wallet_label: 'Jidn',
    wallet_avatar: 'https://stalkchain.nyc3.cdn.digitaloceanspaces.com/assets/images/avatars/kol/jidn_w.jpg',
    isPublic: true,
    isKol: true,
    socials: [ [Object], [Object] ],
    totalUsd: 145,
    totalUsdNumber: 145.000953,
    transactionType: 'buy'
  },
  timestamp: 1748913955272
}
*/
