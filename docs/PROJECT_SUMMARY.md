# Project Summary: Robust API Engine

## Overview
This document summarizes the development of a production-ready API engine with credit-based usage tracking, multi-tier access control, and real-time WebSocket streaming capabilities.

## User Requirements & Preferences

### Core Requirements
- **Credit-based usage tracking** - Primary billing/usage model
- **Multi-tier access control** - Different service levels
- **PostgreSQL database** - Primary data storage
- **Redis pub/sub** - Real-time WebSocket communication
- **Robust/fast performance** - Emphasis on caching and optimization
- **Test organization** - Dedicated test folder structure

### User Preferences (From Memory)
- Credit-based usage tracking over other billing models
- Access tiers with enable/disable functionality in database
- Free tier disabled by default but available for future activation
- Redis pub/sub for WebSocket real-time communication
- PostgreSQL as primary database
- Emphasis on robust/fast performance with caching
- Test files organized in dedicated test folder

## Architecture & Technology Stack

### Backend Framework
- **Node.js** with ES modules
- **Express.js** for REST API
- **WebSocket (ws)** for real-time communication

### Database & Caching
- **PostgreSQL** - Primary database with advanced functions
- **Redis** - Caching and pub/sub for WebSocket communication
- **Connection pooling** for performance optimization

### Security & Authentication
- **JWT tokens** for session-based authentication
- **API keys** for programmatic access
- **bcrypt** for password hashing
- **Rate limiting** with Redis-backed storage
- **Helmet** for security headers

### Real-time Features
- **WebSocket server** with authentication
- **Redis pub/sub** for message broadcasting
- **Stream management** with automatic data generation
- **Subscription-based access control**

## Database Schema

### Core Tables
1. **access_tiers** - Service tier definitions with enable/disable functionality
2. **users** - User accounts with tier assignments
3. **api_usage_logs** - Detailed usage tracking
4. **websocket_sessions** - Active WebSocket connections
5. **stream_definitions** - Available data streams
6. **api_keys** - Alternative authentication method

### Key Features
- **Database functions** for atomic credit consumption
- **Comprehensive indexing** for performance
- **Audit trails** for all operations
- **Tier enable/disable** functionality

## Access Tier System

### Tier Configuration
| Tier | Status | Credits/Month | Requests/Min | WebSocket Connections | Price |
|------|--------|---------------|--------------|----------------------|-------|
| **Free** | Disabled* | 1,000 | 10 | 1 | $0.00 |
| **Basic** | Enabled | 10,000 | 60 | 3 | $9.99 |
| **Premium** | Enabled | 100,000 | 300 | 10 | $49.99 |
| **Enterprise** | Enabled | Unlimited | 1,000 | 50 | $199.99 |

*Free tier disabled by default but can be enabled through admin controls

### Tier Management
- **Enable/disable functionality** without losing configuration
- **Admin API endpoints** for tier management
- **Safety checks** prevent disabling tiers with active users
- **Database-level controls** for immediate effect

## API Endpoints

### Authentication
- `POST /auth/login` - User authentication
- `POST /auth/refresh` - Token refresh
- `GET /auth/profile` - User profile with credit information

### Core API
- `GET /api/v1/demo` - Demo endpoint (all tiers)
- `GET /api/v1/data` - Data endpoint (Basic+)
- `GET /api/v1/analytics` - Analytics endpoint (Premium+)
- `GET /api/v1/search` - Search endpoint (Premium+)
- `POST /api/v1/batch` - Batch processing (Enterprise only)

### WebSocket API
- `GET /ws-api/info` - WebSocket server information
- `GET /ws-api/streams` - Available streams for user's tier
- **`GET /ws-api/credits`** - **Stream credit information and costs**
- **`GET /ws-api/credits?stream=kol-feed`** - **Specific stream credit details**
- **`GET /ws-api/credits/stats`** - **Stream credit usage statistics**

### Admin API (Admin API Key Required)
- `GET /admin/tiers` - List all tiers
- `POST /admin/tiers/{id}/enable` - Enable tier
- `POST /admin/tiers/{id}/disable` - Disable tier
- `PUT /admin/tiers/{id}` - Update tier configuration
- `GET /admin/tiers/{id}/stats` - Tier usage statistics
- `GET /admin/me` - Get admin user info
- `GET /admin/admins` - List all admin users (system admin only)
- `POST /admin/admins` - Create admin user (system admin only)
- `PUT /admin/admins/{id}` - Update admin user (system admin only)

## WebSocket Streaming

### Connection Methods
- JWT token: `ws://localhost:3001/ws?token=JWT_TOKEN`
- API key: `ws://localhost:3001/ws?apiKey=API_KEY`

### Available Streams
- **demo-stream** - Basic demo data (Free+) - 1 credit per message
- **data-stream** - Real-time market data (Basic+) - 2 credits per message
- **kol-feed** - **Real-time KOL trading activity (Basic+) - 2 credits per message**
- **analytics-stream** - Advanced analytics (Premium+) - 5 credits per message
- **enterprise-stream** - High-frequency data (Enterprise only) - 1 credit per message

#### KOL Feed Stream
The KOL feed provides real-time trading activity from Key Opinion Leaders (KOLs) in the cryptocurrency space:
- **Live transaction data** from verified KOL traders
- **Privacy protection** for private wallets
- **Rich metadata** including token information and social profiles
- **Event-driven architecture** using Redis pub/sub
- **Credit-based access** with 2 credits per message received

### Message Types
- `ping/pong` - Connection health
- `subscribe/unsubscribe` - Stream management
- `stream_data` - Real-time data delivery
- `connected/error` - Connection status

## Development Features

### Testing Infrastructure
- **Comprehensive test suite** in `tests/` folder
- **API functionality tests** - All endpoints and authentication
- **WebSocket tests** - Real-time streaming functionality
- **System requirements check** - Environment validation

### Development Tools
- **Hot reload** with `--watch` flag
- **Environment configuration** with `.env` files
- **Database migrations** with SQL scripts
- **Setup verification** script

### Performance Optimizations
- **Redis caching** for user data and API keys
- **Database connection pooling**
- **Efficient indexing strategy**
- **Atomic credit consumption** via database functions

## File Structure
```
nodejs-api/
├── app.js                 # Main application entry
├── package.json           # Dependencies and scripts
├── .env                   # Environment configuration
├── SQL/                   # Database schema and migrations
├── src/
│   ├── config/           # Database and Redis configuration
│   ├── middleware/       # Authentication, rate limiting, credits, stream credits
│   ├── models/           # User model with tier integration
│   ├── routes/           # API route handlers
│   ├── scripts/          # Database migration scripts
│   ├── websocket/        # WebSocket server and stream management
│   └── workers/          # Background workers (KOL feed integration)
├── tests/                # All test files
│   ├── setup-check.js    # System requirements verification
│   ├── test.js           # API functionality tests
│   └── websocket-test.js # WebSocket streaming tests
├── docs/                 # Documentation
│   ├── README.md         # Documentation index
│   ├── API_DOCUMENTATION.md      # Complete API reference
│   ├── SETUP_GUIDE.md            # Installation guide
│   ├── PROJECT_SUMMARY.md        # This file
│   ├── CREDIT_SYSTEM_GUIDE.md    # Credit system documentation
│   ├── POSTMAN_GUIDE.md          # API testing guide
│   └── NGINX_DEPLOYMENT.md       # Production deployment
└── postman/              # API testing collection
    ├── README.md         # Collection documentation
    ├── StalkAPI_Postman_Collection.json     # REST API tests
    └── StalkAPI_Postman_Environment.json    # Environment config
```

## Key Implementation Details

### Credit System
- **Atomic consumption** via PostgreSQL functions for race condition prevention
- **Real-time tracking** with remaining credits and monthly usage
- **Monthly reset** capability with automatic or manual triggers
- **Tier-based limits** with unlimited option (-1) for Enterprise
- **Pre-consumption validation** - credits deducted before API processing
- **Comprehensive logging** - all credit transactions tracked in database
- **Admin management** - credit addition, reset, and analytics via admin API
- **Cost-based pricing** - different credit costs per endpoint complexity

#### Credit Costs by Endpoint

**REST API Endpoints:**
- `/api/v1/demo` - 1 credit (basic functionality)
- `/api/v1/data` - 2 credits (data retrieval)
- `/api/v1/analytics` - 5 credits (complex processing)
- `/api/v1/search` - 3 credits (search algorithms)
- `/api/v1/batch` - 10 credits (bulk operations)

**WebSocket Streams:**
- WebSocket connections - 0 credits (connection is free)
- `demo-stream` - 1 credit per message
- `data-stream` - 2 credits per message
- **`kol-feed`** - **2 credits per message (real-time KOL trading data)**
- `analytics-stream` - 5 credits per message
- `enterprise-stream` - 1 credit per message

**Note:** WebSocket stream credits are charged **per message received**, not per connection.

#### Credit Configuration
- **Middleware**: `src/middleware/credits.js` - defines endpoint costs
- **Database**: `access_tiers` table - defines tier limits
- **Functions**: PostgreSQL `consume_credits()` for atomic operations
- **Admin API**: Credit management and analytics endpoints

### Security Model
- **Multi-factor authentication** (JWT + API keys)
- **Tier-based access control** at endpoint and stream level
- **Admin authentication** with dedicated API keys and permissions
- **Rate limiting** with Redis backing
- **Input validation** and sanitization

### Admin Authentication System
- **Dedicated admin users table** separate from regular users
- **Admin API keys** that never expire (perfect for backend integration)
- **Permission-based access control** with granular permissions:
  - `tiers:read` - View tier information
  - `tiers:write` - Modify tier settings
  - `users:read` - View user information
  - `users:write` - Modify user accounts
  - `analytics:read` - Access analytics data
  - `system:admin` - Full system administration access
- **Audit logging** for all admin actions
- **Cache integration** for performance
- **No JWT token expiration** - ideal for backend services

### Real-time Architecture
- **Redis pub/sub** for message distribution
- **Stream management** with automatic data generation
- **Connection tracking** in database
- **Graceful disconnection** handling

## Current Status

### Completed Features ✅
- Complete database schema with tier management
- Full authentication system (JWT + API keys)
- Credit-based usage tracking with atomic operations
- **WebSocket stream credit system** - Credits per message received
- Multi-tier access control system
- WebSocket server with real-time streaming
- **KOL Feed integration** - Real-time KOL trading activity
- Redis pub/sub integration with event-driven architecture
- **Stream credit management endpoints** - Credit info and usage statistics
- Comprehensive test suite
- Admin API for tier management
- Documentation and setup guides

### Demo User
- **Email**: <EMAIL>
- **Password**: demo123
- **Tier**: Basic (10,000 credits)
- **API Key**: demo_api_key_12345

### Server Configuration
- **Port**: 3001 (configurable via .env)
- **Environment**: Development with hot reload
- **Database**: PostgreSQL with connection pooling
- **Cache**: Redis with pub/sub capabilities

## Future Considerations

### Tier Management
- Free tier is configured but disabled by default
- Can be enabled instantly via admin API or database update
- All tier configurations are preserved and ready for activation

### Scalability
- Database functions ensure atomic operations
- Redis caching reduces database load
- Connection pooling optimizes resource usage
- Modular architecture supports horizontal scaling

### Monitoring
- Comprehensive usage logging
- WebSocket session tracking
- Credit consumption audit trails
- Tier usage statistics via admin API

## Commands Reference

### Development
- `npm run dev` - Start development server with hot reload
- `npm run setup-check` - Verify system requirements
- `npm run db:migrate` - Run database migrations

### Testing
- `npm run test:api` - Test all API endpoints
- `npm run test:websocket` - Test WebSocket functionality

### Production
- `npm start` - Start production server

## Production Deployment

### Domain & Infrastructure
- **Domain**: https://data.stalkapi.com
- **Server Port**: 3001 (internal)
- **Reverse Proxy**: Nginx with Cloudflare origin certificates
- **SSL**: Full (strict) mode with Cloudflare

### Nginx Configuration
- **Rate limiting** at nginx level for additional protection
- **Real IP detection** from Cloudflare headers (CF-Connecting-IP)
- **Security headers** including HSTS, CSP, and XSS protection
- **Gzip compression** for optimal performance
- **WebSocket support** with proper proxy settings

### Real IP Handling
- **Cloudflare integration** with proper IP range configuration
- **Custom IP utility** (`src/utils/ip.js`) for extracting real IPs
- **Middleware integration** throughout the application
- **Rate limiting** based on real IP addresses
- **Usage logging** with accurate IP tracking

## Technical Lessons Learned

### Database Design Decisions
- **PostgreSQL functions** for atomic credit consumption prevent race conditions
- **Composite indexes** significantly improve query performance
- **UUID primary keys** provide better scalability than auto-increment
- **JSONB arrays** for tier permissions offer flexibility without complex joins

### WebSocket Implementation
- **Redis pub/sub** enables horizontal scaling of WebSocket connections
- **Session tracking** in database provides persistence across server restarts
- **Token-based authentication** works seamlessly with existing JWT infrastructure
- **Stream-based architecture** allows granular access control

### Performance Optimizations
- **Redis caching** reduces database queries by ~80%
- **Connection pooling** prevents database connection exhaustion
- **Middleware ordering** affects performance (auth before rate limiting)
- **Database indexes** on frequently queried columns are critical

### Security Considerations
- **Tier validation** must happen at both API and database levels
- **Cache invalidation** is crucial when tier status changes
- **Rate limiting** should be tier-aware for fair usage
- **Admin endpoints** require additional security layers

### Development Workflow
- **Migration scripts** with idempotent operations prevent deployment issues
- **Comprehensive testing** catches integration problems early
- **Environment validation** prevents configuration-related failures
- **Modular architecture** makes feature additions straightforward

## Troubleshooting Guide

### Common Issues Encountered
1. **Port conflicts** - Server running on different port than expected
2. **Cache inconsistency** - User data cached with old tier information
3. **WebSocket authentication** - User model not loading tier permissions
4. **Database migrations** - Column dependencies in wrong order

### Solutions Implemented
1. **Flexible port configuration** via environment variables
2. **Cache invalidation** when tier status changes
3. **Complete user model** with all tier-related properties
4. **Idempotent migrations** with proper dependency checking

## Project Evolution

### Initial Requirements
- Basic API with authentication
- Credit tracking system
- Multi-tier access

### Final Implementation
- Production-ready API engine
- Real-time WebSocket streaming
- Advanced tier management
- Comprehensive admin controls
- Full test coverage
- Performance optimizations

### Key Additions During Development
- **Tier enable/disable functionality** - Added per user request
- **Admin API endpoints** - For tier management
- **WebSocket streaming** - Real-time data capabilities
- **Redis pub/sub integration** - Scalable message distribution
- **KOL Feed integration** - Real-time KOL trading activity stream
- **WebSocket stream credit system** - Credit deduction per message received
- **Stream credit management endpoints** - Credit info, costs, and usage statistics
- **Event-driven architecture** - Workers and Redis pub/sub for scalable data processing
- **Comprehensive testing** - API and WebSocket test suites
- **Performance caching** - Redis-based optimization
- **Nginx configuration** - Production-ready reverse proxy with Cloudflare integration
- **Real IP detection** - Proper handling of user IPs through Cloudflare and nginx
- **Admin authentication system** - Dedicated admin API keys for backend access
- **Permission-based admin access** - Granular permissions for different admin operations

This project represents a complete, production-ready API engine with advanced features for credit-based usage tracking, multi-tier access control, and real-time streaming capabilities. The architecture is designed for scalability, security, and maintainability.
