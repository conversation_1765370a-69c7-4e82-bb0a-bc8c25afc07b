# Credit System Guide

This comprehensive guide explains how the credit-based usage tracking system works in the StalkAPI engine.

## Overview

The credit system provides fair resource allocation and usage tracking by assigning credit costs to API endpoints based on their computational complexity and resource requirements.

## How Credits Work

### Credit Flow
1. **User makes API request** → System checks available credits
2. **Credit validation** → Ensures user has sufficient credits
3. **Credit consumption** → Deducts credits atomically before processing
4. **Request processing** → API endpoint executes and returns data
5. **Usage logging** → Records credit consumption for analytics

### Key Principles
- **Pre-consumption**: Credits are deducted before processing to prevent overuse
- **Atomic operations**: Database functions ensure consistency
- **Real-time tracking**: Immediate credit balance updates
- **Tier-based limits**: Different credit allocations per access tier
- **Monthly reset**: Credits refresh on the 1st of each month

## Credit Costs

### Current Endpoint Costs

| Endpoint | Credits | Justification |
|----------|---------|---------------|
| `GET /api/v1/demo` | 1 | Simple demo data, minimal processing |
| `GET /api/v1/data` | 2 | Database queries, moderate processing |
| `GET /api/v1/analytics` | 5 | Complex calculations, data aggregation |
| `GET /api/v1/search` | 3 | Search algorithms, indexing operations |
| `POST /api/v1/batch` | 10 | Bulk processing, high resource usage |
| WebSocket connections | 0 | Real-time streaming, no connection cost |

### WebSocket Stream Credit Costs

| Stream | Credits per Message | Justification |
|--------|-------------------|---------------|
| `demo-stream` | 1 | Simple demo data, minimal processing |
| `data-stream` | 2 | Real-time data feed, moderate processing |
| `kol-feed` | **2** | **Real-time KOL trading data, external API costs** |
| `analytics-stream` | 5 | Complex analytics, high computational cost |
| `enterprise-stream` | 1 | High-frequency data, optimized for enterprise |

**Important:** WebSocket stream credits are charged **per message received**, not per connection. This ensures fair usage based on actual data consumption.

### Cost Calculation Factors
- **CPU usage**: Computational complexity
- **Database operations**: Number and complexity of queries
- **Memory usage**: Data processing requirements
- **External API calls**: Third-party service costs
- **Response time**: Processing duration

## Configuration

### Setting Credit Costs

**File**: `src/middleware/credits.js`

```javascript
// Credit costs for different endpoints
const CREDIT_COSTS = {
    '/api/v1/demo': 1,
    '/api/v1/data': 2,
    '/api/v1/analytics': 5,
    '/api/v1/search': 3,
    '/api/v1/batch': 10,
    // Add new endpoints here
    '/api/v1/new-feature': 7
};

// Apply credit middleware to routes
export const consumeCredits = (req, res, next) => {
    const endpoint = req.route?.path || req.path;
    const creditsRequired = CREDIT_COSTS[endpoint] || 1; // Default cost
    
    // Credit consumption logic...
};
```

### Tier Credit Limits

**Database**: `access_tiers` table

```sql
-- View current tier limits
SELECT name, max_credits_per_month, price_per_month 
FROM access_tiers 
ORDER BY max_credits_per_month;

-- Update tier limits
UPDATE access_tiers 
SET max_credits_per_month = 50000 
WHERE name = 'premium';

-- Add new tier
INSERT INTO access_tiers (
    name, 
    max_credits_per_month, 
    price_per_month,
    description
) VALUES (
    'pro', 
    25000, 
    24.99,
    'Professional tier with enhanced limits'
);
```

### Environment Configuration

```bash
# .env file
CREDIT_TRACKING_ENABLED=true
CREDIT_GRACE_PERIOD=false
CREDIT_WARNING_THRESHOLD=100
MONTHLY_RESET_ENABLED=true

# WebSocket Stream Credits
STREAM_CREDITS_ENABLED=true
```

## WebSocket Stream Credit System

### Overview

The WebSocket Stream Credit System extends the credit-based usage tracking to real-time WebSocket streams. Unlike REST API endpoints that charge per request, WebSocket streams charge **per message received** by each user.

### How Stream Credits Work

1. **Connection**: WebSocket connections are free (0 credits)
2. **Subscription**: Subscribing to streams is free (0 credits)
3. **Message Delivery**: Each message delivered to a user costs credits based on the stream type
4. **Credit Checking**: Before sending each message, the system checks if the user has sufficient credits
5. **Credit Deduction**: Credits are deducted atomically after successful message delivery
6. **Insufficient Credits**: Users without enough credits receive a warning message instead of stream data

### Stream Credit Configuration

Stream credit costs are configured in the `stream_definitions` database table:

```sql
-- View current stream credit costs
SELECT stream_name, credits_per_message, required_tier_id, is_active
FROM stream_definitions
WHERE is_active = true
ORDER BY stream_name;

-- Update stream credit cost
UPDATE stream_definitions
SET credits_per_message = 3
WHERE stream_name = 'kol-feed';

-- Add new stream with credit cost
INSERT INTO stream_definitions (
    stream_name,
    description,
    required_tier_id,
    credits_per_message,
    max_subscribers,
    is_active
) VALUES (
    'new-stream',
    'New real-time data stream',
    2, -- basic tier
    4, -- 4 credits per message
    100,
    true
);
```

### Stream Credit Manager

The `StreamCreditManager` class handles all stream credit operations:

**File**: `src/middleware/streamCredits.js`

```javascript
import { streamCreditManager } from '../middleware/streamCredits.js';

// Get credit cost for a stream
const cost = streamCreditManager.getStreamCreditCost('kol-feed'); // Returns 2

// Check if user has sufficient credits
const canAfford = await streamCreditManager.checkUserCredits(userId, 'kol-feed');

// Consume credits for a stream message
const success = await streamCreditManager.consumeStreamCredits(userId, 'kol-feed');

// Batch check credits for multiple users
const eligibleUsers = await streamCreditManager.batchCheckCredits(userIds, 'kol-feed');

// Get stream usage statistics
const stats = await streamCreditManager.getStreamStats('kol-feed', 7); // Last 7 days
```

### Credit Deduction Flow

1. **Message Broadcasting**: When a stream has new data to broadcast
2. **User Collection**: Gather all users subscribed to the stream
3. **Batch Credit Check**: Check which users have sufficient credits in a single database query
4. **Selective Broadcasting**: Send messages only to users with sufficient credits
5. **Credit Consumption**: Deduct credits asynchronously after message delivery
6. **Warning Messages**: Send credit warnings to users without sufficient credits

### Stream Credit Endpoints

#### Get Stream Credit Information
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  "http://localhost:3001/ws-api/credits"
```

#### Get Specific Stream Credit Cost
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  "http://localhost:3001/ws-api/credits?stream=kol-feed"
```

#### Get Stream Credit Usage Statistics
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  "http://localhost:3001/ws-api/credits/stats?days=7&stream=kol-feed"
```

## Database Schema

### Core Tables

#### users
```sql
-- Credit-related columns
credits_remaining INTEGER DEFAULT 0,
credits_used_this_month INTEGER DEFAULT 0,
total_credits_purchased INTEGER DEFAULT 0,
tier_id INTEGER REFERENCES access_tiers(id)
```

#### access_tiers
```sql
-- Tier credit configuration
max_credits_per_month INTEGER NOT NULL DEFAULT 0, -- -1 for unlimited
price_per_month DECIMAL(10,2) DEFAULT 0.00,
is_enabled BOOLEAN DEFAULT true
```

#### api_usage_logs
```sql
-- Credit consumption tracking
user_id UUID REFERENCES users(id),
credits_consumed INTEGER NOT NULL,
endpoint VARCHAR(255) NOT NULL,
method VARCHAR(10) NOT NULL,
ip_address INET,
user_agent TEXT,
request_id UUID,
created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
```

### Database Functions

#### consume_credits()
```sql
-- Atomic credit consumption
CREATE OR REPLACE FUNCTION consume_credits(
    p_user_id UUID,
    p_credits_to_consume INTEGER,
    p_endpoint VARCHAR(255),
    p_method VARCHAR(10),
    p_ip_address INET,
    p_user_agent TEXT,
    p_request_id UUID,
    p_tier_id INTEGER,
    p_max_credits INTEGER,
    p_current_month VARCHAR(7)
) RETURNS JSON AS $$
DECLARE
    v_current_credits INTEGER;
    v_credits_used_this_month INTEGER;
    v_result JSON;
BEGIN
    -- Get current credit status
    SELECT credits_remaining, credits_used_this_month 
    INTO v_current_credits, v_credits_used_this_month
    FROM users 
    WHERE id = p_user_id;
    
    -- Check if user has sufficient credits
    IF v_current_credits < p_credits_to_consume THEN
        RETURN json_build_object(
            'success', false,
            'error', 'INSUFFICIENT_CREDITS',
            'credits_remaining', v_current_credits,
            'credits_required', p_credits_to_consume
        );
    END IF;
    
    -- Check monthly limit (if not unlimited)
    IF p_max_credits > 0 AND (v_credits_used_this_month + p_credits_to_consume) > p_max_credits THEN
        RETURN json_build_object(
            'success', false,
            'error', 'CREDIT_LIMIT_EXCEEDED',
            'credits_used_this_month', v_credits_used_this_month,
            'max_credits_per_month', p_max_credits
        );
    END IF;
    
    -- Consume credits
    UPDATE users 
    SET 
        credits_remaining = credits_remaining - p_credits_to_consume,
        credits_used_this_month = credits_used_this_month + p_credits_to_consume
    WHERE id = p_user_id;
    
    -- Log usage
    INSERT INTO api_usage_logs (
        user_id, credits_consumed, endpoint, method, 
        ip_address, user_agent, request_id
    ) VALUES (
        p_user_id, p_credits_to_consume, p_endpoint, p_method,
        p_ip_address, p_user_agent, p_request_id
    );
    
    -- Return success
    RETURN json_build_object(
        'success', true,
        'credits_consumed', p_credits_to_consume,
        'credits_remaining', v_current_credits - p_credits_to_consume
    );
END;
$$ LANGUAGE plpgsql;
```

## Admin Management

### Credit Administration Endpoints

```bash
# View user credit status
curl -X GET "http://localhost:3001/admin/users/{userId}/credits" \
  -H "X-Admin-API-Key: YOUR_ADMIN_API_KEY"

# Add credits to user account
curl -X POST "http://localhost:3001/admin/users/{userId}/credits" \
  -H "X-Admin-API-Key: YOUR_ADMIN_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "credits_to_add": 5000,
    "reason": "Promotional bonus",
    "expires_at": "2024-12-31"
  }'

# Reset user monthly credits
curl -X POST "http://localhost:3001/admin/users/{userId}/credits/reset" \
  -H "X-Admin-API-Key: YOUR_ADMIN_API_KEY"

# Bulk credit operations
curl -X POST "http://localhost:3001/admin/credits/bulk" \
  -H "X-Admin-API-Key: YOUR_ADMIN_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "add",
    "credits": 1000,
    "tier_filter": "basic",
    "reason": "Holiday bonus"
  }'
```

### Credit Analytics

```sql
-- Top credit consumers
SELECT 
    u.email,
    SUM(aul.credits_consumed) as total_credits_used,
    COUNT(*) as request_count,
    AVG(aul.credits_consumed) as avg_credits_per_request
FROM api_usage_logs aul
JOIN users u ON aul.user_id = u.id
WHERE aul.created_at >= date_trunc('month', CURRENT_DATE)
GROUP BY u.email
ORDER BY total_credits_used DESC
LIMIT 10;

-- Credit usage by endpoint
SELECT 
    endpoint,
    SUM(credits_consumed) as total_credits,
    COUNT(*) as request_count,
    AVG(credits_consumed) as avg_credits
FROM api_usage_logs
WHERE created_at >= date_trunc('month', CURRENT_DATE)
GROUP BY endpoint
ORDER BY total_credits DESC;

-- Monthly credit trends
SELECT 
    date_trunc('month', created_at) as month,
    SUM(credits_consumed) as total_credits,
    COUNT(DISTINCT user_id) as unique_users
FROM api_usage_logs
GROUP BY date_trunc('month', created_at)
ORDER BY month DESC;
```

## Best Practices

### For Developers

1. **Cost-based pricing**: Set credit costs based on actual resource usage
2. **Monitor patterns**: Track credit consumption to optimize costs
3. **Graceful degradation**: Provide clear error messages for credit issues
4. **Caching**: Implement caching to reduce credit consumption
5. **Batch operations**: Encourage bulk requests to reduce per-request overhead

### For Administrators

1. **Regular monitoring**: Check credit usage patterns weekly
2. **Tier optimization**: Adjust tier limits based on user behavior
3. **Promotional credits**: Use bonus credits for user retention
4. **Usage alerts**: Set up alerts for unusual consumption patterns
5. **Cost analysis**: Regularly review credit costs vs. actual resource usage

### For Users

1. **Monitor usage**: Check credit balance regularly via `/auth/profile`
2. **Optimize requests**: Use batch endpoints when possible
3. **Tier planning**: Choose appropriate tier based on usage patterns
4. **Cache responses**: Store frequently accessed data locally
5. **Upgrade timing**: Monitor usage to plan tier upgrades

## Troubleshooting

### Common Issues

1. **Credits not deducting**
   - Check if `CREDIT_TRACKING_ENABLED=true`
   - Verify middleware is applied to routes
   - Check database function execution

2. **Negative credit balances**
   - Database constraints prevent this
   - Check for race conditions in high-traffic scenarios
   - Verify atomic operations are working

3. **Monthly reset not working**
   - Check database function `reset_monthly_credits()`
   - Verify cron job or scheduled task
   - Check timezone settings

4. **Incorrect credit costs**
   - Verify `CREDIT_COSTS` mapping in middleware
   - Check route path matching
   - Ensure middleware order is correct

### Debug Commands

```sql
-- Check user credit status
SELECT 
    u.email,
    u.credits_remaining,
    u.credits_used_this_month,
    at.max_credits_per_month,
    at.name as tier_name
FROM users u
JOIN access_tiers at ON u.tier_id = at.id
WHERE u.email = '<EMAIL>';

-- Recent credit transactions
SELECT 
    endpoint,
    method,
    credits_consumed,
    created_at,
    ip_address
FROM api_usage_logs
WHERE user_id = 'user-uuid'
ORDER BY created_at DESC
LIMIT 20;

-- Credit consumption by hour
SELECT 
    date_trunc('hour', created_at) as hour,
    SUM(credits_consumed) as credits_used,
    COUNT(*) as requests
FROM api_usage_logs
WHERE created_at >= CURRENT_DATE
GROUP BY date_trunc('hour', created_at)
ORDER BY hour;
```

This credit system provides a robust foundation for usage-based billing and resource management in the StalkAPI engine.
