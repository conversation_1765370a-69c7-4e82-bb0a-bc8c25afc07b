// WebSocket test client
import WebSocket from 'ws';
import axios from 'axios';

const BASE_URL = 'http://localhost:3001';
const WS_URL = 'ws://localhost:3001/ws';

async function testWebSocket() {
    console.log('🔌 Testing WebSocket functionality...\n');
    
    try {
        // First, get authentication token
        console.log('1. Logging in to get token...');
        const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
            email: '<EMAIL>',
            password: 'demo123'
        });
        
        const token = loginResponse.data.token;
        console.log('   ✅ Login successful');
        
        // Connect to WebSocket
        console.log('\n2. Connecting to WebSocket...');
        const ws = new WebSocket(`${WS_URL}?token=${token}`);
        
        ws.on('open', () => {
            console.log('   ✅ WebSocket connected successfully');
            
            // Test ping
            console.log('\n3. Testing ping...');
            ws.send(JSON.stringify({
                type: 'ping'
            }));
            
            // Subscribe to demo stream
            setTimeout(() => {
                console.log('\n4. Subscribing to demo-stream...');
                ws.send(JSON.stringify({
                    type: 'subscribe',
                    payload: { stream: 'demo-stream' }
                }));
            }, 1000);
            
            // Subscribe to data stream (if available)
            setTimeout(() => {
                console.log('\n5. Subscribing to data-stream...');
                ws.send(JSON.stringify({
                    type: 'subscribe',
                    payload: { stream: 'data-stream' }
                }));
            }, 2000);
            
            // Unsubscribe after some time
            setTimeout(() => {
                console.log('\n6. Unsubscribing from demo-stream...');
                ws.send(JSON.stringify({
                    type: 'unsubscribe',
                    payload: { stream: 'demo-stream' }
                }));
            }, 10000);
            
            // Close connection after test
            setTimeout(() => {
                console.log('\n7. Closing WebSocket connection...');
                ws.close();
            }, 15000);
        });
        
        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data.toString());
                
                switch (message.type) {
                    case 'connected':
                        console.log('   📡 Connection established:', message.message);
                        break;
                        
                    case 'pong':
                        console.log('   🏓 Pong received');
                        break;
                        
                    case 'subscribed':
                        console.log(`   ✅ Subscribed to: ${message.stream}`);
                        break;
                        
                    case 'unsubscribed':
                        console.log(`   ❌ Unsubscribed from: ${message.stream}`);
                        break;
                        
                    case 'stream_data':
                        console.log(`   📊 Stream data from ${message.stream}:`, 
                            JSON.stringify(message.data).substring(0, 100) + '...');
                        break;
                        
                    case 'error':
                        console.log('   ❌ WebSocket error:', message.error);
                        break;
                        
                    default:
                        console.log('   📨 Unknown message type:', message.type);
                }
            } catch (error) {
                console.log('   📨 Raw message:', data.toString());
            }
        });
        
        ws.on('error', (error) => {
            console.error('   ❌ WebSocket error:', error.message);
        });
        
        ws.on('close', (code, reason) => {
            console.log(`   🔌 WebSocket closed: ${code} - ${reason}`);
            console.log('\n🎉 WebSocket test completed!');
            process.exit(0);
        });
        
    } catch (error) {
        console.error('❌ WebSocket test failed:', error.response?.data || error.message);
        process.exit(1);
    }
}

// Run WebSocket test
testWebSocket();
