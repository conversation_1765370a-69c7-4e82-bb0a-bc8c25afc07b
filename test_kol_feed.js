import { WebSocket } from 'ws';

// Demo user API key from the database
const API_KEY = '2fF24O8HUvmHYAOUzPigSJc3acLMDS4LbGe9Gg54OfRWScE4OG';
const WS_URL = `ws://localhost:3001/ws?apiKey=${API_KEY}`;

console.log('🔌 Connecting to WebSocket...');
const ws = new WebSocket(WS_URL);

ws.on('open', () => {
    console.log('✅ Connected to WebSocket');
    
    // Subscribe to kol-feed stream
    const subscribeMessage = {
        type: 'subscribe',
        payload: {
            stream: 'kol-feed'
        }
    };
    
    console.log('📡 Subscribing to kol-feed stream...');
    ws.send(JSON.stringify(subscribeMessage));
});

ws.on('message', (data) => {
    try {
        const message = JSON.parse(data.toString());
        console.log('📨 Received message:', JSON.stringify(message, null, 2));
        
        if (message.type === 'stream_data' && message.stream === 'kol-feed') {
            console.log('🎯 KOL FEED DATA:', JSON.stringify(message.data, null, 2));
        }
    } catch (error) {
        console.error('❌ Error parsing message:', error);
        console.log('Raw message:', data.toString());
    }
});

ws.on('error', (error) => {
    console.error('❌ WebSocket error:', error);
});

ws.on('close', (code, reason) => {
    console.log(`🔌 WebSocket closed: ${code} - ${reason}`);
});

// Keep the process alive
process.on('SIGINT', () => {
    console.log('\n👋 Closing WebSocket connection...');
    ws.close();
    process.exit(0);
});

console.log('🎯 Waiting for KOL feed messages...');
console.log('Press Ctrl+C to exit');
